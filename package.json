{"name": "react-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.542.0", "qs": "^6.14.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.2", "tailwind-merge": "^3.3.1", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.33.0", "@tailwindcss/vite": "^4.1.13", "@types/node": "^24.3.1", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react-swc": "^4.0.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.13", "tw-animate-css": "^1.3.8", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}