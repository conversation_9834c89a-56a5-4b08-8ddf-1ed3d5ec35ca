import { Routes, Route, Link } from 'react-router-dom'
import Home from '@/routes/Home'
import About from '@/routes/About'

export default function App() {
  return (
    <div className="min-h-dvh">
      <nav className="sticky top-0 border-b bg-background/75 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto flex gap-4 p-4">
          <Link to="/" className="font-semibold">Template</Link>
          <Link to="/about" className="text-muted-foreground hover:text-foreground">About</Link>
        </div>
      </nav>
      <main className="container mx-auto p-4">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/about" element={<About />} />
        </Routes>
      </main>
    </div>
  )
}
