import { But<PERSON> } from "@/components/ui/button"
import { useCounter } from "@/stores/counter"
import { Link } from "react-router-dom"

export default function Home() {
  const { count, inc, dec, reset } = useCounter()

  return (
    <div className="p-6 space-y-4">
      <h1 className="text-2xl font-bold">Home</h1>

      <div className="flex items-center gap-2">
        <Button variant="secondary" onClick={dec}>
          -
        </Button>
        <span className="w-10 text-center font-mono tabular-nums">{count}</span>
        <Button onClick={inc}>+</Button>
        <Button variant="outline" onClick={reset}>
          Reset
        </Button>
      </div>

      <div className="space-x-3">
        <Link to="/about" className="underline text-primary">
          About
        </Link>
      </div>
    </div>
  )
}

