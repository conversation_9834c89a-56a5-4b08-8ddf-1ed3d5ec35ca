import axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type CustomParamsSerializer,
} from "axios";

import type {
  RequestMethods,
  CrsRequestConfig,
  CrsRequestResponse,
  CrsReauestError,
} from "./request.d";

import qs from "qs";

const deaultConfig: AxiosRequestConfig = {
  timeout: 1000 * 30,
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest",
  },
};

