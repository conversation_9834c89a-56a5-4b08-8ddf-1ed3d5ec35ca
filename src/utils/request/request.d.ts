import type {   Method,
  AxiosError,
  AxiosResponse,
  AxiosRequestConfig } from "axios";

export type RequestMethods =Extract< Method, "GET" | "POST" | "PUT" | "DELETE" | "PATCH" | "HEAD" | "OPTIONS" | "CONNECT" | "TRACE">;


export interface CrsReauestError extends AxiosError {
  isCancelRequest: boolean;
}

export interface CrsRequestConfig extends AxiosRequestConfig {
    beforeRequestCallback?: (requset: CrsRequestConfig) => void;
    beforeResponseCallback?: (response: CrsRequestResponse) => void;
}


export interface CrsRequestResponse extends AxiosResponse {
  config: CrsRequestConfig;
}

export default class CrsHttpRequest {
  request<T>(
    method: RequestMethods,
    url: string,
    params?: AxiosRequestConfig,
    axiosConfig?: CrsRequestConfig
  ): Promise<T>;
  get<T,P>(
    url: string,
    params?: P,
    config?: CrsRequestConfig
  ): Promise<T>;
  post<T,P>(
    url: string,
    params?: P,
    config?: CrsRequestConfig
  ): Promise<T>;
}

