export type SupportedLanguagesType = "en-US" | "zh-CN";

export type ImportLocalFn = () => Promise<{ default: Record<string, string> }>;

export type LoadMessagesFn = (
  lang: SupportedLanguagesType
) => Promise<Record<string, string> | undefined>;


export interface LocaleSetupOptions {
  //@default zh-CN
  defaultLocale?: SupportedLanguagesType;

  loadMessages?: LoadMessagesFn;

  missingWarn?: boolean;
}
